package com.sayweee.central.merchandising.rule.model;

import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * 规则执行请求类
 * <p>
 * 用于封装规则执行所需的所有信息，包括规则标识、执行上下文、配置参数等
 *
 * <AUTHOR>
 * @since 2025/5/15
 */
@Data
public class RuleExecutionRequest {

    /**
     * 唯一请求ID
     * 用于标识和追踪规则执行请求
     */
    private String requestId;

    /**
     * 规则集/规则文件的标识符
     * 用于指定要执行的规则集或规则文件，不包含.drl扩展名
     */
    private String rulesetId;

    /**
     * 规则执行上下文
     * 包含规则执行所需的业务数据和参数，可以包含任意类型的业务对象
     */
    private Map<String, Object> context = new HashMap<>();

    /**
     * 规则执行配置
     * 包含规则执行的各种配置参数，如超时时间、最大执行规则数等
     */
    private RuleExecutionConfig config;

    /**
     * 用户/系统标识
     * 标识发起规则执行的用户或系统，用于审计和权限控制
     */
    private String executedBy;

    /**
     * 业务标识符
     * 用于关联和跟踪特定业务流程或业务对象
     */
    private String businessKey;

    /**
     * 元数据
     * 存储与规则执行相关的额外信息，不影响规则执行逻辑
     */
    private Map<String, Object> metadata = new HashMap<>();


}
