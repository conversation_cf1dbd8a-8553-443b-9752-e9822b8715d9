package com.sayweee.central.merchandising.rule.config;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.PreDestroy;
import org.kie.api.runtime.KieSession;
import org.kie.api.runtime.KieContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * KieSession池管理类，用于管理Drools规则引擎会话的生命周期
 *
 * <p>主要功能：
 * <ul>
 *   <li>维护可重用的KieSession对象池</li>
 *   <li>控制最大并发会话数量</li>
 *   <li>自动清理会话状态</li>
 *   <li>提供会话监控指标</li>
 * </ul>
 *
 * <p>线程安全：所有公共方法都做了线程安全处理
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Component
public class KieSessionPool {
    /**
     * 空闲会话队列 (线程安全)
     */
    private final Queue<KieSession> sessionPool = new ConcurrentLinkedQueue<>();

    /**
     * 活跃会话计数器 (原子操作)
     */
    private final AtomicInteger activeCount = new AtomicInteger(0);

    /**
     * 最大池大小，从配置读取，默认10
     */
    @Value("${drools.session.pool.max-size:10}")
    private int maxPoolSize;

    /**
     * Drools容器，用于创建新会话
     */
    @Autowired
    private KieContainer kieContainer;

    /**
     * 从池中借用一个KieSession
     *
     * @return 可用的KieSession对象，如果达到最大限制可能返回null
     * @throws IllegalStateException 如果创建会话失败
     */
    public synchronized KieSession borrowSession() throws IllegalStateException {
        try {
            // 1. 尝试从空闲队列获取
            KieSession session = sessionPool.poll();

            // 2. 如果池为空且未达上限，创建新会话
            if (session == null && activeCount.get() < maxPoolSize) {
                session = createNewSession();
            }

            // 3. 增加活跃计数
            if (session != null) {
                activeCount.incrementAndGet();
            }
            return session;
        } catch (Exception e) {
            throw new IllegalStateException("Failed to borrow KieSession", e);
        }
    }

    /**
     * 归还KieSession到池中
     *
     * @param session 要归还的会话对象，如果为null则忽略
     */
    public void returnSession(KieSession session) {
        if (session != null) {
            try {
                // 1. 清理所有事实对象
                session.getFactHandles().forEach(session::delete);

                // 2. 重置所有全局变量
                session.getGlobals().getGlobalKeys().forEach(key ->
                    session.setGlobal(key, null));

                // 3. 清空议程
                session.getAgenda().clear();

                // 4. 放回池中并减少计数
                sessionPool.offer(session);
                activeCount.decrementAndGet();
            } catch (Exception e) {
                // 清理失败时直接销毁会话
                session.dispose();
            }
        }
    }

    /**
     * 创建新的KieSession实例
     *
     * @return 新创建的KieSession
     */
    private KieSession createNewSession() {
        return kieContainer.newKieSession();
    }

    /**
     * 容器销毁时清理所有资源
     */
    @PreDestroy
    public void destroy() {
        sessionPool.forEach(session -> {
            try {
                session.dispose();
            } catch (Exception e) {
                // 忽略销毁异常
            }
        });
        sessionPool.clear();
    }

    /**
     * 获取当前活跃会话数
     *
     * @return 正在使用的会话数量
     */
    public int getActiveCount() {
        return activeCount.get();
    }

    /**
     * 获取空闲会话数
     *
     * @return 池中可用的会话数量
     */
    public int getIdleCount() {
        return sessionPool.size();
    }
}
