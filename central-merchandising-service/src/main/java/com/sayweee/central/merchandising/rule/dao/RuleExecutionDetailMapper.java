package com.sayweee.central.merchandising.rule.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sayweee.central.merchandising.rule.entity.RuleExecutionDetail;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 规则执行详情Mapper接口
 * <p>
 * 提供rule_execution_detail表的CRUD及扩展查询
 *
 * <AUTHOR>
 * @since 2025/5/16
 */
@Mapper
public interface RuleExecutionDetailMapper extends BaseMapper<RuleExecutionDetail> {

    /**
     * 根据执行ID查询规则执行详情
     * @param executionId 执行记录ID
     * @return 规则执行详情列表
     */
    @Select("SELECT * FROM rule_execution_detail WHERE execution_id = #{executionId}")
    List<RuleExecutionDetail> selectByExecutionId(@Param("executionId") Long executionId);

    /**
     * 根据规则ID查询执行详情
     * @param ruleId 规则ID
     * @return 规则执行详情列表
     */
    @Select("SELECT * FROM rule_execution_detail WHERE rule_id = #{ruleId}")
    List<RuleExecutionDetail> selectByRuleId(@Param("ruleId") String ruleId);

    /**
     * 根据字段名查询执行详情
     * @param fieldName 校验字段名称
     * @return 规则执行详情列表
     */
    @Select("SELECT * FROM rule_execution_detail WHERE field_name = #{fieldName}")
    List<RuleExecutionDetail> selectByFieldName(@Param("fieldName") String fieldName);

    /**
     * 根据规则结果查询执行详情
     * @param result 规则结果(FAIL/PASS/NEEDS_INPUT)
     * @return 规则执行详情列表
     */
    @Select("SELECT * FROM rule_execution_detail WHERE result = #{result}")
    List<RuleExecutionDetail> selectByResult(@Param("result") String result);

    /**
     * 根据规则级别查询执行详情
     * @param level 规则级别(BLOCKING/MANUAL_REVIEW_TRIGGER)
     * @return 规则执行详情列表
     */
    @Select("SELECT * FROM rule_execution_detail WHERE level = #{level}")
    List<RuleExecutionDetail> selectByLevel(@Param("level") String level);
}
