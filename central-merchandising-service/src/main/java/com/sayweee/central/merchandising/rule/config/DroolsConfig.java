package com.sayweee.central.merchandising.rule.config;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.kie.api.KieBase;
import org.kie.api.KieServices;
import org.kie.api.builder.*;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.kie.internal.io.ResourceFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.util.Assert;

import lombok.extern.slf4j.Slf4j;

/**
 * Drools规则引擎配置类
 *
 * <p>核心职责：
 * <ul>
 *   <li>加载和管理Drools规则文件(.drl)</li>
 *   <li>创建和配置KieContainer</li>
 *   <li>提供KieBase和KieSession的Bean定义</li>
 *   <li>管理规则引擎生命周期</li>
 * </ul>
 *
 * <p>配置参数：
 * <ul>
 *   <li>drools.rules.path - 规则文件路径，默认rules/</li>
 *   <li>drools.rules.encoding - 规则文件编码，默认UTF-8</li>
 *   <li>drools.enabled - 是否启用规则引擎，默认true</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Slf4j
@Configuration
public class DroolsConfig {

    /**
     * 规则文件路径配置
     */
    @Value("${drools.rules.path:rules/}")
    private String rulesPath;

    /**
     * 规则文件编码配置
     */
    @Value("${drools.rules.encoding:UTF-8}")
    private String rulesEncoding;

    /**
     * 是否启用Drools引擎
     */
    @Value("${drools.enabled:true}")
    private boolean droolsEnabled;

    /**
     * KieContainer实例
     */
    private KieContainer kieContainer;

    /**
     * 规则文件缓存
     */
    private final Map<String, Resource> ruleResources = new HashMap<>();

    /**
     * 配置校验
     */
    @PostConstruct
    public void validateConfig() {
        Assert.hasText(rulesPath, "Drools rules path must not be empty");
        Assert.hasText(rulesEncoding, "Drools rules encoding must not be empty");
        log.info("Drools configuration validated successfully");
    }

    /**
     * 创建KieContainer Bean
     *
     * @return 配置好的KieContainer实例
     */
    @Bean
    public KieContainer kieContainer() {
        if (this.kieContainer != null) {
            return this.kieContainer;
        }

        KieServices kieServices = getKieServices();

        if (!droolsEnabled) {
            log.info("Drools rule engine is disabled. Returning empty KieContainer.");
            this.kieContainer = createEmptyContainer(kieServices);
            return this.kieContainer;
        }

        try {
            Resource[] resources = loadRuleFiles();
            if (resources.length == 0) {
                log.warn("No DRL rule files found in path: {}", rulesPath);
                this.kieContainer = createEmptyContainer(kieServices);
                return this.kieContainer;
            }

            log.info("Found {} DRL rule files in path: {}", resources.length, rulesPath);
            cacheRuleResources(resources);

            KieFileSystem kieFileSystem = createKieFileSystem(kieServices, resources);
            KieBuilder kieBuilder = buildKieModule(kieServices, kieFileSystem);
            validateBuildResults(kieBuilder.getResults());

            log.info("Drools rules built successfully. Creating KieContainer.");
            this.kieContainer = kieServices.newKieContainer(kieServices.getRepository().getDefaultReleaseId());
            return this.kieContainer;
        } catch (Exception e) {
            log.error("Failed to create KieContainer", e);
            log.warn("Fallback to empty KieContainer");
            this.kieContainer = createEmptyContainer(kieServices);
            return this.kieContainer;
        }
    }

    /**
     * 创建KieBase Bean
     *
     * @return 配置好的KieBase实例
     */
    @Bean
    public KieBase kieBase() {
        try {
            return kieContainer().getKieBase();
        } catch (Exception e) {
            log.error("Failed to create KieBase", e);
            return createEmptyContainer(getKieServices()).getKieBase();
        }
    }

    /**
     * 创建prototype作用域的KieSession Bean
     *
     * @return 新的KieSession实例
     */
    @Bean
    @Scope("prototype")
    public KieSession kieSession() {
        try {
            return kieContainer().newKieSession();
        } catch (Exception e) {
            log.error("Failed to create KieSession", e);
            return createEmptyContainer(getKieServices()).newKieSession();
        }
    }

    /**
     * 根据规则集ID获取对应的KieSession
     *
     * @param rulesetId 规则集ID
     * @return 配置好的KieSession实例
     */
    public KieSession getSessionForRuleset(String rulesetId) {
        if (rulesetId == null || ruleResources.isEmpty()) {
            log.debug("Using default session");
            return kieSession();
        }

        if (ruleResources.containsKey(rulesetId)) {
            log.debug("Creating session for ruleset: {}", rulesetId);
            return kieSession();
        }

        log.warn("Ruleset ID '{}' not found, using default session", rulesetId);
        return kieSession();
    }

    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void destroy() {
        if (kieContainer != null) {
            log.info("Disposing KieContainer");
            kieContainer.dispose();
        }
    }

    // ========== 私有方法 ==========

    private Resource[] loadRuleFiles() throws IOException {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        String effectivePath = rulesPath.endsWith("/") ? rulesPath : rulesPath + "/";
        String pattern = "classpath*:" + effectivePath + "**/*.drl";

        log.debug("Loading rule files with pattern: {}", pattern);
        return Arrays.stream(resolver.getResources(pattern))
            .filter(this::isValidResource)
            .toArray(Resource[]::new);
    }

    private boolean isValidResource(Resource resource) {
        try {
            return resource.exists() && resource.isReadable();
        } catch (Exception e) {
            log.warn("Invalid resource: {}", resource.getFilename(), e);
            return false;
        }
    }

    private void cacheRuleResources(Resource[] resources) {
        for (Resource resource : resources) {
            String filename = resource.getFilename();
            if (filename != null) {
                String rulesetId = filename.replace(".drl", "");
                ruleResources.put(rulesetId, resource);
                log.debug("Cached rule: {}", rulesetId);
            }
        }
    }

    private KieFileSystem createKieFileSystem(KieServices kieServices, Resource[] resources) {
        KieFileSystem kfs = kieServices.newKieFileSystem();
        for (Resource resource : resources) {
            String path = rulesPath + resource.getFilename();
            log.debug("Loading rule: {}", path);
            kfs.write(ResourceFactory.newClassPathResource(path, rulesEncoding));
        }
        return kfs;
    }

    private KieBuilder buildKieModule(KieServices kieServices, KieFileSystem kfs) {
        KieBuilder kieBuilder = kieServices.newKieBuilder(kfs);
        kieBuilder.buildAll();
        return kieBuilder;
    }

    private void validateBuildResults(Results results) {
        if (results.hasMessages(Message.Level.ERROR)) {
            results.getMessages(Message.Level.ERROR).forEach(msg ->
                log.error("Rule error: {}", msg.getText()));
        }
    }

    private KieContainer createEmptyContainer(KieServices kieServices) {
        return kieServices.newKieContainer(kieServices.getRepository().getDefaultReleaseId());
    }

    private KieServices getKieServices() {
        return KieServices.Factory.get();
    }
}
