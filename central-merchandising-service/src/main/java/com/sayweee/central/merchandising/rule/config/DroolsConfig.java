package com.sayweee.central.merchandising.rule.config;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.kie.api.KieBase;
import org.kie.api.KieServices;
import org.kie.api.builder.*;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.kie.internal.io.ResourceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.util.Assert;

import lombok.extern.slf4j.Slf4j;

/**
 * Drools规则引擎配置类（池化版本）
 */
@Slf4j
@Configuration
public class DroolsConfig {

    @Value("${drools.rules.path:rules/}")
    private String rulesPath;

    @Value("${drools.rules.encoding:UTF-8}")
    private String rulesEncoding;

    @Value("${drools.enabled:true}")
    private boolean droolsEnabled;

    private KieContainer kieContainer;
    private final Map<String, Resource> ruleResources = new HashMap<>();

    @Autowired
    private KieSessionPool kieSessionPool;

    @PostConstruct
    public void validateConfig() {
        Assert.hasText(rulesPath, "Drools rules path must not be empty");
        Assert.hasText(rulesEncoding, "Drools rules encoding must not be empty");
        log.info("Drools configuration validated successfully");
    }

    /**
     * 核心修改：只提供KieContainer，不直接提供KieSession
     */
    @Bean
    public KieContainer kieContainer() {
        if (this.kieContainer != null) {
            return this.kieContainer;
        }

        KieServices kieServices = getKieServices();

        if (!droolsEnabled) {
            log.info("Drools rule engine is disabled. Returning empty KieContainer.");
            this.kieContainer = createEmptyContainer(kieServices);
            return this.kieContainer;
        }

        try {
            Resource[] resources = loadRuleFiles();
            if (resources.length == 0) {
                log.warn("No DRL rule files found in path: {}", rulesPath);
                this.kieContainer = createEmptyContainer(kieServices);
                return this.kieContainer;
            }

            log.info("Found {} DRL rule files in path: {}", resources.length, rulesPath);
            cacheRuleResources(resources);

            KieFileSystem kieFileSystem = createKieFileSystem(kieServices, resources);
            KieBuilder kieBuilder = buildKieModule(kieServices, kieFileSystem);
            validateBuildResults(kieBuilder.getResults());

            log.info("Drools rules built successfully. Creating KieContainer.");
            this.kieContainer = kieServices.newKieContainer(kieServices.getRepository().getDefaultReleaseId());
            return this.kieContainer;
        } catch (Exception e) {
            log.error("Failed to create KieContainer", e);
            log.warn("Fallback to empty KieContainer");
            this.kieContainer = createEmptyContainer(kieServices);
            return this.kieContainer;
        }
    }

    /**
     * 修改：通过池获取KieSession
     */
    public KieSession getSession() {
        try {
            return kieSessionPool.borrowSession();
        } catch (Exception e) {
            log.error("Failed to get session from pool", e);
            return createEmptyContainer(getKieServices()).newKieSession();
        }
    }

    /**
     * 修改：通过池获取指定规则集的KieSession
     */
    public KieSession getSessionForRuleset(String rulesetId) {
        try {
            // 实际实现应根据规则集ID从池中获取特定会话
            return kieSessionPool.borrowSession();
        } catch (Exception e) {
            log.error("Failed to get session for ruleset: {}", rulesetId, e);
            return createEmptyContainer(getKieServices()).newKieSession();
        }
    }

    /**
     * 创建KieSession Bean
     * 每次注入时都会创建新的实例
     */
    @Bean
    @Scope("prototype")
    public KieSession kieSession() {
        try {
            return kieContainer().newKieSession();
        } catch (Exception e) {
            log.error("Failed to create KieSession: {}", e.getMessage(), e);
            // 返回空容器的session作为fallback
            return createEmptyContainer(getKieServices()).newKieSession();
        }
    }

    @PreDestroy
    public void destroy() {
        if (kieContainer != null) {
            log.info("Disposing KieContainer");
            kieContainer.dispose();
        }
    }

    // 以下私有方法保持不变...
    private Resource[] loadRuleFiles() throws IOException {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        String effectivePath = rulesPath.endsWith("/") ? rulesPath : rulesPath + "/";
        String pattern = "classpath*:" + effectivePath + "**/*.drl";
        return Arrays.stream(resolver.getResources(pattern))
            .filter(this::isValidResource)
            .toArray(Resource[]::new);
    }

    private boolean isValidResource(Resource resource) {
        try {
            return resource.exists() && resource.isReadable();
        } catch (Exception e) {
            log.warn("Invalid resource: {}", resource.getFilename(), e);
            return false;
        }
    }

    private void cacheRuleResources(Resource[] resources) {
        for (Resource resource : resources) {
            String filename = resource.getFilename();
            if (filename != null) {
                String rulesetId = filename.replace(".drl", "");
                ruleResources.put(rulesetId, resource);
            }
        }
    }

    private KieFileSystem createKieFileSystem(KieServices kieServices, Resource[] resources) {
        KieFileSystem kfs = kieServices.newKieFileSystem();
        for (Resource resource : resources) {
            String path = rulesPath + resource.getFilename();
            kfs.write(ResourceFactory.newClassPathResource(path, rulesEncoding));
        }
        return kfs;
    }

    private KieBuilder buildKieModule(KieServices kieServices, KieFileSystem kfs) {
        KieBuilder kieBuilder = kieServices.newKieBuilder(kfs);
        kieBuilder.buildAll();
        return kieBuilder;
    }

    private void validateBuildResults(Results results) {
        if (results.hasMessages(Message.Level.ERROR)) {
            results.getMessages(Message.Level.ERROR).forEach(msg ->
                log.error("Rule error: {}", msg.getText()));
        }
    }

    private KieContainer createEmptyContainer(KieServices kieServices) {
        return kieServices.newKieContainer(kieServices.getRepository().getDefaultReleaseId());
    }

    private KieServices getKieServices() {
        return KieServices.Factory.get();
    }
}
