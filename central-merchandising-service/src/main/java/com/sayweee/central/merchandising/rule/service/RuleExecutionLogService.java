package com.sayweee.central.merchandising.rule.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sayweee.central.merchandising.rule.dao.RuleExecutionDetailMapper;
import com.sayweee.central.merchandising.rule.dao.RuleExecutionMapper;
import com.sayweee.central.merchandising.rule.model.RuleExecutionResponse;
import com.sayweee.central.merchandising.rule.entity.RuleExecution;
import com.sayweee.central.merchandising.rule.entity.RuleExecutionDetail;
import com.sayweee.central.merchandising.rule.model.GenericRuleResult;
import com.sayweee.central.merchandising.rule.model.RuleExecutionRequest;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> lifei
 * @Classname RuleExecutionLogService
 * @Description TODO
 * @Date 2025/5/16 10:35
 */
@Slf4j
@Service
public class RuleExecutionLogService {


    @Resource
    private RuleExecutionMapper ruleExecutionMapper;

    @Resource
    private RuleExecutionDetailMapper ruleExecutionDetailMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 记录规则执行主记录
     */
    @Transactional
    public RuleExecution logRuleExecution(RuleExecutionRequest request, RuleExecutionResponse response) {
        try {
            RuleExecution execution = new RuleExecution();
            execution.setRequestId(request.getRequestId());
            execution.setRulesetId(request.getRulesetId());
            execution.setBusinessKey(request.getBusinessKey());
            execution.setExecutedBy(request.getExecutedBy());
            execution.setExecutionTime(LocalDateTime.now());
            execution.setExecutionDurationMs(response.getExecutionTimeMillis());
            execution.setStatus(response.getStatus());
            execution.setConclusion(response.getConclusion() != null ? response.getConclusion().toString() : null);
            execution.setRulesExecutedCount(response.getRulesExecutedCount());

            // 序列化上下文数据(仅包含重要信息)
            try {
                String contextJson = objectMapper.writeValueAsString(request.getContext());
                // 限制长度，避免数据过大
                if (contextJson.length() > 4000) {
                    contextJson = contextJson.substring(0, 3990) + "...";
                }
                execution.setContextData(contextJson);
            } catch (Exception e) {
                log.warn("Failed to serialize context data: {}", e.getMessage());
                execution.setContextData("Failed to serialize context data");
            }

            execution.setErrorMessage(response.getErrorMessage());

            // 使用MyBatis-Plus保存主记录
            ruleExecutionMapper.insert(execution);

            // 设置执行ID到响应对象
            response.setExecutionId(execution.getId());

            return execution;
        } catch (Exception e) {
            log.error("Failed to log rule execution: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 记录规则执行详情
     */
    @Transactional
    public void logRuleExecutionDetails(RuleExecution execution, Map<String, Object> ruleResults) {
        if (execution == null || ruleResults == null || ruleResults.isEmpty()) {
            return;
        }

        try {
            List<RuleExecutionDetail> details = new ArrayList<>();
            AtomicInteger order = new AtomicInteger(1);

            // 收集所有规则执行详情
            ruleResults.forEach((key, value) -> {
                // 跳过聚合结论
                if (value instanceof String && key.equals("conclusion")) {
                    return;
                }

                if (value instanceof GenericRuleResult) {
                    GenericRuleResult result = (GenericRuleResult) value;

                    RuleExecutionDetail detail = new RuleExecutionDetail();
                    detail.setExecutionId(execution.getId()); // 使用外键
                    detail.setRuleId(result.getRuleId());
                    detail.setRuleName(result.getRuleName());
                    detail.setLevel(result.getLevel());
                    detail.setResult(result.getResult());
                    detail.setReason(result.getReason());
                    detail.setExecutionTimeMs(result.getExecutionTimeMillis());
                    detail.setFired(result.isFired());
                    detail.setExecutionOrder(order.getAndIncrement());

                    details.add(detail);
                }
            });

            // 批量插入详情记录
            if (!details.isEmpty()) {
                int insertCount = ruleExecutionDetailMapper.insertBatch(details);
                log.debug("批量插入规则执行详情，插入数量: {}", insertCount);
            }
        } catch (Exception e) {
            log.error("Failed to log rule execution details: {}", e.getMessage(), e);
            throw new RuntimeException("记录规则执行详情失败", e);
        }
    }

    /**
     * 根据执行ID获取执行记录
     */
    public RuleExecution getExecutionById(Long executionId) {
        return ruleExecutionMapper.selectById(executionId);
    }

    /**
     * 根据请求ID获取执行记录
     */
    public RuleExecution getExecutionByRequestId(String requestId) {
        return ruleExecutionMapper.selectByRequestId(requestId);
    }

    /**
     * 获取执行记录的详情
     */
    public java.util.List<RuleExecutionDetail> getExecutionDetails(Long executionId) {
        return ruleExecutionDetailMapper.selectByExecutionId(executionId);
    }


}
