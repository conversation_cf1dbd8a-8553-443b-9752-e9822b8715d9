package com.sayweee.central.merchandising.rule.exception;

/**
 * 规则执行超时异常
 * <p>
 * 当规则执行时间超过配置的超时时间时抛出此异常
 *
 * <AUTHOR>
 * @since 2025/5/16
 */
public class RuleExecutionTimeoutException extends RuleEngineException {

    private static final String DEFAULT_ERROR_CODE = "RULE_TIMEOUT";

    public RuleExecutionTimeoutException(String message) {
        super(DEFAULT_ERROR_CODE, message);
    }

    public RuleExecutionTimeoutException(String message, Throwable cause) {
        super(DEFAULT_ERROR_CODE, message, cause);
    }

    public RuleExecutionTimeoutException(long timeoutMs) {
        super(DEFAULT_ERROR_CODE, String.format("规则执行超时，超时时间: %dms", timeoutMs));
    }

    public RuleExecutionTimeoutException(String rulesetId, long timeoutMs) {
        super(DEFAULT_ERROR_CODE, String.format("规则集 %s 执行超时，超时时间: %dms", rulesetId, timeoutMs));
    }
}
