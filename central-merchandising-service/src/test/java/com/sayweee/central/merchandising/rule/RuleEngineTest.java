package com.sayweee.central.merchandising.rule;

import com.sayweee.central.merchandising.rule.config.DroolsConfig;
import com.sayweee.central.merchandising.rule.model.RuleExecutionRequest;
import com.sayweee.central.merchandising.rule.model.RuleExecutionResponse;
import com.sayweee.central.merchandising.rule.service.GenericRuleService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 规则引擎测试类
 * 验证规则引擎的基本功能是否正常
 */
@SpringBootTest
@ActiveProfiles("test")
public class RuleEngineTest {

    @Autowired
    private GenericRuleService ruleService;

    @Autowired
    private DroolsConfig droolsConfig;

    @Test
    public void testDroolsConfigInitialization() {
        // 测试Drools配置是否正确初始化
        assertNotNull(droolsConfig, "DroolsConfig应该被正确注入");
        assertNotNull(droolsConfig.kieContainer(), "KieContainer应该被正确创建");
        assertNotNull(droolsConfig.kieSession(), "KieSession应该被正确创建");
    }

    @Test
    public void testRuleServiceInitialization() {
        // 测试规则服务是否正确初始化
        assertNotNull(ruleService, "GenericRuleService应该被正确注入");
    }

    @Test
    public void testBasicRuleExecution() {
        // 创建测试请求
        RuleExecutionRequest request = createTestRequest();
        
        // 执行规则
        RuleExecutionResponse response = ruleService.executeRules(request);
        
        // 验证响应
        assertNotNull(response, "响应不应该为空");
        assertNotNull(response.getRequestId(), "请求ID不应该为空");
        assertEquals(request.getRequestId(), response.getRequestId(), "请求ID应该匹配");
        
        // 验证执行状态
        assertTrue("SUCCESS".equals(response.getStatus()) || "ERROR".equals(response.getStatus()), 
                  "状态应该是SUCCESS或ERROR");
        
        // 如果执行成功，验证基本结果
        if ("SUCCESS".equals(response.getStatus())) {
            assertNotNull(response.getRuleResults(), "规则结果不应该为空");
            assertTrue(response.getExecutionTimeMillis() >= 0, "执行时间应该大于等于0");
        }
    }

    @Test
    public void testProductValidationRule() {
        // 创建产品验证规则测试请求
        RuleExecutionRequest request = createProductValidationRequest();
        
        // 执行规则
        RuleExecutionResponse response = ruleService.executeRules(request);
        
        // 验证响应
        assertNotNull(response, "响应不应该为空");
        assertEquals("product_validation", request.getRulesetId(), "规则集ID应该匹配");
        
        // 打印结果用于调试
        System.out.println("规则执行状态: " + response.getStatus());
        System.out.println("执行时间: " + response.getExecutionTimeMillis() + "ms");
        System.out.println("规则执行数量: " + response.getRulesExecutedCount());
        
        if (response.getErrorMessage() != null) {
            System.out.println("错误信息: " + response.getErrorMessage());
        }
        
        if (response.getRuleResults() != null) {
            System.out.println("规则结果数量: " + response.getRuleResults().size());
            response.getRuleResults().forEach((key, value) -> {
                System.out.println("规则: " + key + " -> " + value);
            });
        }
    }

    /**
     * 创建基本测试请求
     */
    private RuleExecutionRequest createTestRequest() {
        RuleExecutionRequest request = new RuleExecutionRequest();
        request.setRequestId(UUID.randomUUID().toString());
        request.setRulesetId("product_validation");
        request.setBusinessKey("TEST-001");
        request.setExecutedBy("test-user");
        
        // 创建简单的上下文
        Map<String, Object> context = new HashMap<>();
        context.put("testField", "testValue");
        request.setContext(context);
        
        return request;
    }

    /**
     * 创建产品验证规则测试请求
     */
    private RuleExecutionRequest createProductValidationRequest() {
        RuleExecutionRequest request = new RuleExecutionRequest();
        request.setRequestId(UUID.randomUUID().toString());
        request.setRulesetId("product_validation");
        request.setBusinessKey("PRODUCT-123");
        request.setExecutedBy("test-user");
        
        // 创建产品验证上下文
        Map<String, Object> context = new HashMap<>();
        
        // 系统字段
        Map<String, String> systemFields = new HashMap<>();
        systemFields.put("Storage Type", "N");
        systemFields.put("Country of Origin", "China");
        context.put("systemFields", systemFields);
        
        // AI字段
        Map<String, String> aiFields = new HashMap<>();
        aiFields.put("Storage Type", "F");
        aiFields.put("Country of Origin", "China");
        context.put("aiFields", aiFields);
        
        // 成分列表
        context.put("ingredients", java.util.Arrays.asList("三氯乙烷", "糖", "淀粉"));
        
        // Prop65标签
        context.put("hasProp65Label", false);
        
        request.setContext(context);
        
        return request;
    }
}
